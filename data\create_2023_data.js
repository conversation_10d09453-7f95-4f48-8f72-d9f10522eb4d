/**
 * 创建仅包含2023年数据的处理脚本
 * 从原始CSV中提取2023年数据，用于地图展示
 */

import { readFileSync, existsSync, mkdirSync, writeFileSync } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";

// ES模块中的__dirname替代方案
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 简单的CSV解析器
function parseCSV(csvContent) {
  const lines = csvContent.split("\n");
  const headers = lines[0].split(",");
  const data = [];

  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) {
      continue;
    }

    const values = line.split(",");
    const row = {};

    headers.forEach((header, index) => {
      row[header.trim()] = values[index] ? values[index].trim() : "";
    });

    data.push(row);
  }

  return data;
}

// 2023年数据处理器
class Data2023Processor {
  constructor() {
    this.zipCodeData = new Map();
  }

  // 处理CSV数据，只保留2023年
  processCSVData(csvPath) {
    console.log("读取CSV文件:", csvPath);

    const csvContent = readFileSync(csvPath, "utf8");
    const rows = parseCSV(csvContent);

    console.log(`解析到 ${rows.length} 行数据`);

    // 只处理2023年的数据
    const rows2023 = rows.filter((row) => parseInt(row.year) === 2023);
    console.log(`2023年数据: ${rows2023.length} 行`);

    // 处理每一行2023年数据
    rows2023.forEach((row) => {
      const zipCode = row.zip_code;
      const birthCount = parseFloat(row.birth_count) || 0;
      const facilityCapacity = parseFloat(row.facility_capacity) || 0;
      const saturationRate = parseFloat(row.saturation_rate) || 0;

      // 过滤无效数据
      if (
        !zipCode ||
        birthCount <= 0 ||
        facilityCapacity <= 0 ||
        saturationRate <= 0
      ) {
        return;
      }

      // 直接存储2023年数据
      this.zipCodeData.set(zipCode, {
        zipCode,
        birthCount,
        facilityCapacity,
        saturationRate,
        year: 2023,
      });
    });

    console.log(`处理完成，2023年有效ZIP码: ${this.zipCodeData.size}`);
  }

  // 生成加州陆地坐标 - 均匀分布在整个加州
  generateCACoordinates(zipCode) {
    const zipStr = zipCode.toString();

    // 生成基础随机数
    const latHash = this.djb2Hash(zipStr + "latitude_salt_123");
    const lngHash = this.djb2Hash(zipStr + "longitude_salt_456");
    const regionHash = this.djb2Hash(zipStr + "region_salt_789");

    const random1 = (latHash % 1000000) / 1000000;
    const random2 = (lngHash % 1000000) / 1000000;
    const regionRandom = (regionHash % 1000000) / 1000000;

    // 定义加州的多个均匀分布区域
    let lat, lng;

    if (regionRandom < 0.15) {
      // 最北部 - 雷丁、奇科地区
      lat = 39.5 + (41.8 - 39.5) * random1;
      lng = -122.5 + (-120.0 + 122.5) * random2;
    } else if (regionRandom < 0.3) {
      // 北加州 - 萨克拉门托、斯托克顿地区
      lat = 37.8 + (39.5 - 37.8) * random1;
      lng = -122.0 + (-119.5 + 122.0) * random2;
    } else if (regionRandom < 0.45) {
      // 旧金山湾区内陆 - 避开海湾
      lat = 37.0 + (38.0 - 37.0) * random1;
      lng = -122.0 + (-120.5 + 122.0) * random2;
    } else if (regionRandom < 0.6) {
      // 中央谷地 - 弗雷斯诺、贝克斯菲尔德地区
      lat = 35.0 + (37.5 - 35.0) * random1;
      lng = -120.5 + (-118.5 + 120.5) * random2;
    } else if (regionRandom < 0.75) {
      // 南加州内陆 - 河滨、圣贝纳迪诺地区
      lat = 33.5 + (35.5 - 33.5) * random1;
      lng = -118.0 + (-115.0 + 118.0) * random2;
    } else if (regionRandom < 0.9) {
      // 洛杉矶内陆地区
      lat = 33.8 + (34.5 - 33.8) * random1;
      lng = -118.5 + (-117.0 + 118.5) * random2;
    } else {
      // 圣地亚哥内陆地区
      lat = 32.6 + (33.5 - 32.6) * random1;
      lng = -117.5 + (-116.0 + 117.5) * random2;
    }

    // 海洋安全检查和调整
    if (this.isInOcean(lng, lat)) {
      // 如果在海洋中，移动到最近的安全内陆位置
      if (lat > 38) {
        // 北部 - 移到萨克拉门托谷地
        lat = 38.5 + (39.5 - 38.5) * random1;
        lng = -121.5 + (-120.0 + 121.5) * random2;
      } else if (lat > 35) {
        // 中部 - 移到中央谷地
        lat = 36.0 + (37.0 - 36.0) * random1;
        lng = -120.0 + (-119.0 + 120.0) * random2;
      } else {
        // 南部 - 移到内陆帝国
        lat = 33.8 + (34.5 - 33.8) * random1;
        lng = -117.5 + (-116.5 + 117.5) * random2;
      }
    }

    return [lng, lat];
  }

  // 简单的海洋检查函数
  isInOcean(lng, lat) {
    // 检查是否在明显的海洋区域
    if (lng < -124.0) {
      return true;
    } // 太平洋
    if (lat > 40.5 && lng < -123.0) {
      return true;
    } // 北加州海岸
    if (lat > 37.5 && lat < 40.5 && lng < -122.5) {
      return true;
    } // 旧金山湾区海岸
    if (lat > 34.0 && lat < 37.5 && lng < -121.0) {
      return true;
    } // 中加州海岸
    if (lat < 34.0 && lng < -119.5) {
      return true;
    } // 南加州海岸
    return false;
  }

  // DJB2 哈希算法 - 产生更好的分布
  djb2Hash(str) {
    let hash = 5381;
    for (let i = 0; i < str.length; i++) {
      hash = (hash << 5) + hash + str.charCodeAt(i);
    }
    return Math.abs(hash);
  }

  // 确定饱和度等级
  getSaturationLevel(saturation) {
    if (saturation <= 0.5) {
      return "low";
    }
    if (saturation <= 1.0) {
      return "medium";
    }
    if (saturation <= 2.0) {
      return "high";
    }
    return "very-high";
  }

  // 获取等级显示文本
  getSaturationLevelText(level) {
    const levelTexts = {
      low: "低饱和度",
      medium: "中等饱和度",
      high: "高饱和度",
      "very-high": "极高饱和度",
    };
    return levelTexts[level] || level;
  }

  // 生成2023年最终数据
  generate2023Data() {
    const processedData = [];

    for (const [zipCode, data] of this.zipCodeData) {
      const coordinates = this.generateCACoordinates(zipCode);
      const saturationLevel = this.getSaturationLevel(data.saturationRate);

      processedData.push({
        zip_code: zipCode,
        total_births: data.birthCount,
        total_capacity: data.facilityCapacity,
        saturation: Math.round(data.saturationRate * 100) / 100,
        saturation_level: saturationLevel,
        saturation_level_text: this.getSaturationLevelText(saturationLevel),
        latitude: coordinates[1],
        longitude: coordinates[0],
        year: 2023,
      });
    }

    // 按饱和度排序
    processedData.sort((a, b) => b.saturation - a.saturation);
    return processedData;
  }

  // 生成2023年GeoJSON
  generate2023GeoJSON(processedData) {
    const features = processedData.map((item) => ({
      type: "Feature",
      geometry: {
        type: "Point",
        coordinates: [item.longitude, item.latitude],
      },
      properties: {
        zip_code: item.zip_code,
        total_births: item.total_births,
        total_capacity: item.total_capacity,
        saturation: item.saturation,
        saturation_level: item.saturation_level,
        saturation_level_text: item.saturation_level_text,
        year: 2023,
      },
    }));

    return {
      type: "FeatureCollection",
      metadata: {
        title: "California Daycare Saturation 2023",
        description: "2023年加州托儿所市场饱和度数据",
        year: 2023,
        total_features: features.length,
        generated_at: new Date().toISOString(),
      },
      features,
    };
  }

  // 生成2023年统计信息
  generate2023Stats(processedData) {
    const saturations = processedData.map((item) => item.saturation);
    const births = processedData.map((item) => item.total_births);
    const capacities = processedData.map((item) => item.total_capacity);

    return {
      year: 2023,
      total_zip_codes: processedData.length,
      saturation: {
        min: Math.min(...saturations),
        max: Math.max(...saturations),
        avg:
          Math.round(
            (saturations.reduce((sum, val) => sum + val, 0) /
              saturations.length) *
              100
          ) / 100,
      },
      births: {
        min: Math.min(...births),
        max: Math.max(...births),
        avg: Math.round(
          births.reduce((sum, val) => sum + val, 0) / births.length
        ),
        total: births.reduce((sum, val) => sum + val, 0),
      },
      capacity: {
        min: Math.min(...capacities),
        max: Math.max(...capacities),
        avg: Math.round(
          capacities.reduce((sum, val) => sum + val, 0) / capacities.length
        ),
        total: capacities.reduce((sum, val) => sum + val, 0),
      },
      level_distribution: {
        low: processedData.filter((item) => item.saturation_level === "low")
          .length,
        medium: processedData.filter(
          (item) => item.saturation_level === "medium"
        ).length,
        high: processedData.filter((item) => item.saturation_level === "high")
          .length,
        very_high: processedData.filter(
          (item) => item.saturation_level === "very-high"
        ).length,
      },
      generated_at: new Date().toISOString(),
    };
  }

  // 保存2023年数据
  save2023Data(processedData) {
    const outputDir = join(__dirname, "processed_2023");

    // 创建输出目录
    if (!existsSync(outputDir)) {
      mkdirSync(outputDir, { recursive: true });
    }

    // 生成并保存GeoJSON
    const geoJSON = this.generate2023GeoJSON(processedData);
    writeFileSync(
      join(outputDir, "daycare_data_2023.geojson"),
      JSON.stringify(geoJSON, null, 2)
    );

    // 保存处理后的数据
    writeFileSync(
      join(outputDir, "processed_data_2023.json"),
      JSON.stringify(processedData, null, 2)
    );

    // 生成并保存统计信息
    const stats = this.generate2023Stats(processedData);
    writeFileSync(
      join(outputDir, "stats_2023.json"),
      JSON.stringify(stats, null, 2)
    );

    // 保存API数据（简化版）
    const apiData = processedData.map((item) => ({
      zip_code: item.zip_code,
      total_births: item.total_births,
      total_capacity: item.total_capacity,
      saturation: item.saturation,
      saturation_level: item.saturation_level,
      latitude: item.latitude,
      longitude: item.longitude,
    }));

    writeFileSync(
      join(outputDir, "api_data_2023.json"),
      JSON.stringify(apiData, null, 2)
    );

    console.log("2023年数据已保存到:", outputDir);
    return { processedData, geoJSON, stats, apiData };
  }
}

// 主函数
function main() {
  try {
    console.log("=== 开始处理2023年数据 ===\n");

    const processor = new Data2023Processor();
    const csvPath = join(__dirname, "zip_code_saturation.csv");

    // 1. 处理CSV数据（仅2023年）
    processor.processCSVData(csvPath);

    // 2. 生成最终数据
    const processedData = processor.generate2023Data();

    // 3. 保存所有数据
    const result = processor.save2023Data(processedData);

    // 4. 显示统计信息
    console.log("\n=== 2023年数据统计 ===");
    console.log(`总ZIP码数量: ${result.stats.total_zip_codes}`);
    console.log(
      `饱和度范围: ${result.stats.saturation.min} - ${result.stats.saturation.max}`
    );
    console.log(`平均饱和度: ${result.stats.saturation.avg}`);
    console.log(`总出生数: ${result.stats.births.total.toLocaleString()}`);
    console.log(`总容量: ${result.stats.capacity.total.toLocaleString()}`);

    console.log("\n2023年饱和度等级分布:");
    console.log(`  低饱和度 (≤0.5): ${result.stats.level_distribution.low}`);
    console.log(
      `  中等饱和度 (0.5-1.0): ${result.stats.level_distribution.medium}`
    );
    console.log(
      `  高饱和度 (1.0-2.0): ${result.stats.level_distribution.high}`
    );
    console.log(
      `  极高饱和度 (>2.0): ${result.stats.level_distribution.very_high}`
    );

    console.log("\n=== 2023年数据处理完成 ===");
    console.log("生成的文件:");
    console.log("- processed_2023/daycare_data_2023.geojson (GeoJSON格式)");
    console.log("- processed_2023/processed_data_2023.json (完整数据)");
    console.log("- processed_2023/api_data_2023.json (API数据)");
    console.log("- processed_2023/stats_2023.json (统计信息)");
  } catch (error) {
    console.error("处理失败:", error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url.includes("create_2023_data.js")) {
  main();
}

export default Data2023Processor;
