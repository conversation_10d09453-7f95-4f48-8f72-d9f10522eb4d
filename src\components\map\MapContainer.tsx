"use client";

import mapboxgl from "mapbox-gl";
import React, { useEffect, useRef, useState } from "react";
import { useMapLanguage } from "@/hooks/useMapLanguage";
import type {
  MapContainerProps,
  DaycareGeoJSON,
  DaycareFeature,
} from "@/types/daycare";
import { CALIFORNIA_CENTER, CALIFORNIA_BOUNDS } from "@/types/daycare";
import {
  extractZipCoordinates,
  updateDaycareGeoJSONCoordinates,
} from "@/utils/zipCodeCoordinates";

import "mapbox-gl/dist/mapbox-gl.css";

/**
 * 地图容器组件 - 负责Mapbox地图的初始化和数据展示
 */
const MapContainer: React.FC<MapContainerProps> = ({
  className = "",
  height = "100%",
  onMapLoad,
  onError,
}) => {
  const { t } = useMapLanguage();
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<mapboxgl.Map | null>(null);
  const popupRef = useRef<mapboxgl.Popup | null>(null);
  const [, setIsMapLoaded] = useState(false);
  const [showZipBoundaries, setShowZipBoundaries] = useState(false);
  const [zipBoundariesData, setZipBoundariesData] = useState<any>(null);
  const [isZipDataLoaded, setIsZipDataLoaded] = useState(false);
  const [zipCoordinates, setZipCoordinates] = useState<
    Map<string, [number, number]>
  >(new Map());
  const [isLoading, setIsLoading] = useState(true);
  const [loadingStatus, setLoadingStatus] = useState("");
  const [isShowingBoundaries, setIsShowingBoundaries] = useState(false);

  // 初始化地图
  useEffect(() => {
    if (!mapContainerRef.current) {
      return;
    }

    // 设置初始加载状态
    setLoadingStatus(t("map:loading.initializing"));

    try {
      // 设置Mapbox访问令牌
      mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN!;

      if (!mapboxgl.accessToken) {
        throw new Error("Mapbox access token is required");
      }

      // 创建地图实例
      mapRef.current = new mapboxgl.Map({
        container: mapContainerRef.current,
        style: "mapbox://styles/mapbox/light-v11",
        center: CALIFORNIA_CENTER,
        zoom: 7,
        maxBounds: CALIFORNIA_BOUNDS,
        attributionControl: false,
      });

      const map = mapRef.current;

      // 添加导航控件
      map.addControl(new mapboxgl.NavigationControl(), "top-right");

      // 添加比例尺
      map.addControl(
        new mapboxgl.ScaleControl({
          maxWidth: 100,
          unit: "metric",
        }),
        "bottom-right"
      );

      // 地图加载完成事件
      map.on("load", async () => {
        setIsMapLoaded(true);
        setLoadingStatus(t("map:loading.loadingBoundaries"));

        // 先预加载ZIP边界数据，然后加载托儿所数据
        await preloadZipBoundaries();

        setLoadingStatus(t("map:loading.loadingData"));
        await loadDaycareData();

        setLoadingStatus(t("map:loading.complete"));
        setIsLoading(false);
        onMapLoad?.();
      });

      // 错误处理
      map.on("error", (e) => {
        console.error("Mapbox error:", e);
        onError?.(new Error("地图加载失败"));
      });
    } catch (error) {
      console.error("Map initialization error:", error);
      setLoadingStatus("地图加载失败");
      setIsLoading(false);
      onError?.(error as Error);
    }

    // 清理函数
    return () => {
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
    };
  }, [onMapLoad, onError, t]);

  // 加载托儿所数据
  const loadDaycareData = async (): Promise<void> => {
    if (!mapRef.current) {
      return;
    }

    try {
      const response = await fetch("/api/daycare-data-2023?format=geojson");
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || "数据加载失败");
      }

      let geoJsonData: DaycareGeoJSON = result.data;
      console.log("原始数据点数量:", geoJsonData.features.length);

      // 如果ZIP坐标数据已加载，更新托儿所数据的坐标
      if (zipCoordinates.size > 0) {
        console.log("使用ZIP边界坐标更新托儿所位置...");
        geoJsonData = updateDaycareGeoJSONCoordinates(
          geoJsonData,
          zipCoordinates
        );
      } else {
        console.log("ZIP坐标数据未加载，使用原始坐标");
      }

      addDataToMap(geoJsonData);
    } catch (error) {
      console.error("Error loading daycare data:", error);
      onError?.(error as Error);
    }
  };

  // 将数据添加到地图
  const addDataToMap = (geoJsonData: DaycareGeoJSON) => {
    if (!mapRef.current) {
      return;
    }

    const map = mapRef.current;

    // 检查并移除已存在的图层和数据源
    if (map.getLayer("daycare-fill")) {
      map.removeLayer("daycare-fill");
    }
    if (map.getLayer("daycare-stroke")) {
      map.removeLayer("daycare-stroke");
    }
    if (map.getSource("daycare-data-2023")) {
      map.removeSource("daycare-data-2023");
    }

    // 添加数据源
    map.addSource("daycare-data-2023", {
      type: "geojson",
      data: geoJsonData,
    });

    console.warn("数据源已添加，开始添加图层");

    // 添加ZIP码区域填充图层
    map.addLayer({
      id: "daycare-fill",
      type: "fill",
      source: "daycare-data-2023",
      paint: {
        // 根据饱和度等级设置填充颜色
        "fill-color": [
          "match",
          ["get", "saturation_level"],
          "low",
          "#22c55e", // 绿色 - 低饱和度
          "medium",
          "#eab308", // 黄色 - 中等饱和度
          "high",
          "#f97316", // 橙色 - 高饱和度
          "very-high",
          "#ef4444", // 红色 - 极高饱和度
          "#6b7280", // 默认灰色
        ],
        "fill-opacity": 0.7,
      },
    });

    // 添加ZIP码区域边界线图层
    map.addLayer({
      id: "daycare-stroke",
      type: "line",
      source: "daycare-data-2023",
      paint: {
        "line-color": "#ffffff",
        "line-width": 1,
        "line-opacity": 0.8,
      },
    });

    // 添加ZIP码标签图层
    map.addLayer({
      id: "daycare-labels",
      type: "symbol",
      source: "daycare-data-2023",
      layout: {
        "text-field": ["get", "zip_code"],
        "text-font": ["Open Sans Semibold", "Arial Unicode MS Bold"],
        "text-size": [
          "interpolate",
          ["linear"],
          ["zoom"],
          8,
          10, // 缩放级别8时字体大小10px
          10,
          12, // 缩放级别10时字体大小12px
          12,
          14, // 缩放级别12时字体大小14px
          14,
          16, // 缩放级别14时字体大小16px
        ],
        "text-anchor": "center",
        "text-justify": "center",
        "text-allow-overlap": false,
        "text-ignore-placement": false,
        "symbol-placement": "point",
      },
      paint: {
        "text-color": "#ffffff",
        "text-halo-color": "#000000",
        "text-halo-width": 2,
        "text-opacity": [
          "interpolate",
          ["linear"],
          ["zoom"],
          7,
          0, // 缩放级别7以下不显示
          8,
          0.6, // 缩放级别8开始显示
          10,
          0.8,
          12,
          1, // 缩放级别12以上完全显示
        ],
      },
      minzoom: 7, // 只在缩放级别7以上显示标签
    });

    console.warn("ZIP码区域图层已添加");

    // 添加点击事件（监听填充图层）
    map.on("click", "daycare-fill", handleAreaClick);

    // 添加鼠标悬停效果
    map.on("mouseenter", "daycare-fill", () => {
      map.getCanvas().style.cursor = "pointer";
    });

    map.on("mouseleave", "daycare-fill", () => {
      map.getCanvas().style.cursor = "";
    });
  };

  // 预加载ZIP边界数据（不显示在地图上）
  const preloadZipBoundaries = async () => {
    if (isZipDataLoaded) {
      console.log("ZIP boundaries data already loaded");
      return zipBoundariesData;
    }

    try {
      console.log("Preloading ZIP boundaries data...");
      const response = await fetch("/api/zip-boundaries");
      const result = await response.json();

      if (!result.success) {
        console.warn("ZIP boundaries not available:", result.error);
        return null;
      }

      console.log(`ZIP boundaries data preloaded: ${result.count} features`);
      setZipBoundariesData(result.data);

      // 提取ZIP坐标映射
      setLoadingStatus("计算ZIP坐标...");
      const coordinates = extractZipCoordinates(result.data);
      setZipCoordinates(coordinates);
      console.log(`Extracted coordinates for ${coordinates.size} ZIP codes`);

      setIsZipDataLoaded(true);
      return result.data;
    } catch (error) {
      console.error("Error preloading ZIP boundaries:", error);
      setLoadingStatus("ZIP数据加载失败");
      return null;
    }
  };

  // 将ZIP边界添加到地图
  const addZipBoundariesToMap = async (geoJsonData?: any) => {
    console.log("addZipBoundariesToMap called with data:", !!geoJsonData);
    console.log("zipBoundariesData available:", !!zipBoundariesData);

    if (!mapRef.current) {
      console.error("Map ref is null in addZipBoundariesToMap");
      return;
    }

    const map = mapRef.current;

    // 使用传入的数据或预加载的数据
    const dataToUse = geoJsonData || zipBoundariesData;

    if (!dataToUse) {
      console.warn("No ZIP boundaries data available");
      return;
    }

    console.log("Using data with features:", dataToUse.features?.length || 0);

    // 设置显示状态
    setIsShowingBoundaries(true);

    // 使用 requestAnimationFrame 来避免阻塞UI
    await new Promise((resolve) => requestAnimationFrame(resolve));

    // 检查并移除已存在的ZIP边界图层和数据源
    if (map.getLayer("zip-boundaries-fill")) {
      map.removeLayer("zip-boundaries-fill");
    }
    if (map.getLayer("zip-boundaries-line")) {
      map.removeLayer("zip-boundaries-line");
    }
    if (map.getSource("zip-boundaries")) {
      map.removeSource("zip-boundaries");
    }

    // 添加ZIP边界数据源
    map.addSource("zip-boundaries", {
      type: "geojson",
      data: dataToUse,
      // 性能优化选项
      lineMetrics: false,
      generateId: true,
      buffer: 0, // 减少缓冲区
      tolerance: 0.375, // 简化几何形状
    });

    // 使用 requestAnimationFrame 分批添加图层
    await new Promise((resolve) => requestAnimationFrame(resolve));

    // 添加填充图层（半透明）
    map.addLayer({
      id: "zip-boundaries-fill",
      type: "fill",
      source: "zip-boundaries",
      paint: {
        "fill-color": "#3b82f6",
        "fill-opacity": [
          "interpolate",
          ["linear"],
          ["zoom"],
          6,
          0.05, // 低缩放级别时更透明
          10,
          0.1, // 中等缩放级别
          14,
          0.15, // 高缩放级别时更明显
        ],
      },
      // 只在特定缩放级别显示以提高性能
      minzoom: 6,
    });

    await new Promise((resolve) => requestAnimationFrame(resolve));

    // 添加边界线图层
    map.addLayer({
      id: "zip-boundaries-line",
      type: "line",
      source: "zip-boundaries",
      paint: {
        "line-color": "#3b82f6",
        "line-width": [
          "interpolate",
          ["linear"],
          ["zoom"],
          6,
          0.3, // 低缩放级别时更细
          10,
          0.8, // 中等缩放级别
          14,
          1.5, // 高缩放级别时更粗
        ],
        "line-opacity": [
          "interpolate",
          ["linear"],
          ["zoom"],
          6,
          0.4, // 低缩放级别时更透明
          10,
          0.7, // 中等缩放级别
          14,
          0.9, // 高缩放级别时更明显
        ],
      },
      minzoom: 6,
    });

    setIsShowingBoundaries(false);
    console.log("ZIP boundaries added to map");
  };

  // 显示ZIP边界（使用预加载的数据）
  const showZipBoundariesOnMap = async () => {
    console.log("showZipBoundariesOnMap called");

    if (!mapRef.current) {
      console.error("Map ref is null");
      return;
    }

    if (!isZipDataLoaded || !zipBoundariesData) {
      console.warn("ZIP boundaries data not loaded yet, loading now...");
      // 如果数据还没加载，先加载数据
      const data = await preloadZipBoundaries();
      if (data) {
        await addZipBoundariesToMap(data);
      }
      return;
    }

    console.log("Using preloaded ZIP boundaries data");
    await addZipBoundariesToMap();
  };

  // 隐藏ZIP边界
  const hideZipBoundaries = () => {
    if (mapRef.current) {
      const map = mapRef.current;
      if (map.getLayer("zip-boundaries-fill")) {
        map.removeLayer("zip-boundaries-fill");
      }
      if (map.getLayer("zip-boundaries-line")) {
        map.removeLayer("zip-boundaries-line");
      }
      if (map.getSource("zip-boundaries")) {
        map.removeSource("zip-boundaries");
      }
      console.log("ZIP boundaries hidden");
    }
  };

  // 切换ZIP边界显示
  const toggleZipBoundaries = () => {
    console.log("toggleZipBoundaries called");
    console.log("Current showZipBoundaries:", showZipBoundaries);

    setShowZipBoundaries(!showZipBoundaries);
    console.log("Toggle ZIP boundaries:", !showZipBoundaries);

    if (!showZipBoundaries) {
      console.log("About to show ZIP boundaries");
      showZipBoundariesOnMap();
    } else {
      console.log("About to hide ZIP boundaries");
      hideZipBoundaries();
    }
  };
  // 处理区域点击事件
  const handleAreaClick = (
    e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }
  ) => {
    if (!e.features || e.features.length === 0) {
      return;
    }

    const feature = e.features[0] as any as DaycareFeature;
    const props = feature.properties;

    // 关闭现有弹窗
    if (popupRef.current) {
      popupRef.current.remove();
    }

    // 创建新弹窗，使用延迟显示避免位置跳跃
    const popup = new mapboxgl.Popup({
      offset: 25,
      closeButton: true,
      closeOnClick: true,
    })
      .setLngLat(e.lngLat)
      .setHTML(createPopupHTML(props));

    // 使用 setTimeout 确保弹窗在正确位置显示
    setTimeout(() => {
      popup.addTo(mapRef.current!);
      popupRef.current = popup;
    }, 0);

    // 弹窗关闭事件
    popup.on("close", () => {
      popupRef.current = null;
    });
  };

  // 获取饱和度级别的翻译文本
  const getSaturationLevelText = (level: string): string => {
    switch (level) {
      case "low":
        return t("map:legend.lowSaturation");
      case "medium":
        return t("map:legend.mediumSaturation");
      case "high":
        return t("map:legend.highSaturation");
      case "very-high":
        return t("map:legend.veryHighSaturation");
      default:
        return level;
    }
  };

  // 创建弹窗HTML内容
  const createPopupHTML = (props: DaycareFeature["properties"]): string => {
    const saturationLevel = props.saturation_level.replace("-", "-");
    const saturationLevelText = getSaturationLevelText(props.saturation_level);

    return `
      <div class="daycare-popup">
        <div class="popup-header">
          <div class="popup-close" onclick="this.closest('.mapboxgl-popup').remove()"></div>
          <h3>${t("map:popup.title")}</h3>
          <div class="zip-badge">${t("map:popup.zipCode")} ${
      props.zip_code
    }</div>
        </div>

        <div class="popup-content">
          <div class="popup-stats">
            <div class="stat-item">
              <div class="stat-value">${props.total_capacity.toLocaleString()}</div>
              <div class="stat-label">${t("map:popup.totalCapacity")}</div>
            </div>

            <div class="stat-item">
              <div class="stat-value">${props.total_births.toLocaleString()}</div>
              <div class="stat-label">${t("map:popup.totalBirths")}</div>
            </div>
          </div>

          <div class="saturation-indicator ${saturationLevel}">
            <div>
              <div class="saturation-text">${saturationLevelText}</div>
              <div style="font-size: 12px; color: #64748b; margin-top: 2px;">${t(
                "map:popup.saturationLevel"
              )}</div>
            </div>
            <div class="saturation-value">${props.saturation.toFixed(2)}</div>
          </div>
        </div>
      </div>
    `;
  };

  // 飞行到指定位置
  const flyTo = (coordinates: [number, number], zoom: number = 12) => {
    if (!mapRef.current) {
      return;
    }

    mapRef.current.flyTo({
      center: coordinates,
      zoom,
      duration: 2000,
    });
  };

  // 监听自定义事件来处理飞行到指定位置
  useEffect(() => {
    const handleMapFlyTo = (event: CustomEvent) => {
      const { coordinates, zoom } = event.detail;
      flyTo(coordinates, zoom);
    };

    window.addEventListener("mapFlyTo", handleMapFlyTo as EventListener);

    return () => {
      window.removeEventListener("mapFlyTo", handleMapFlyTo as EventListener);
    };
  }, []);

  return (
    <>
      <div className="relative" style={{ height }}>
        <div
          ref={mapContainerRef}
          className={`map-container ${className}`}
          style={{ height: "100%", width: "100%" }}
        />
        {/* Loading 覆盖层 */}
        {isLoading && (
          <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-20">
            <div className="text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
              <div className="text-gray-700 font-medium">{loadingStatus}</div>
              <div className="text-gray-500 text-sm mt-2">
                {t("map:loading.pleaseWait")}
              </div>
            </div>
          </div>
        )}

        {/* ZIP边界控制按钮 */}
        <button
          onClick={toggleZipBoundaries}
          className={`absolute top-0 right-12 z-10 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
            showZipBoundaries
              ? "bg-blue-600 text-white hover:bg-blue-700"
              : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300"
          }`}
          title={
            showZipBoundaries
              ? t("map:boundaries.hide")
              : t("map:boundaries.show")
          }
          disabled={
            (!isZipDataLoaded && !showZipBoundaries) || isShowingBoundaries
          }
        >
          {!isZipDataLoaded && !showZipBoundaries
            ? t("map:common.loading")
            : isShowingBoundaries
            ? t("map:loading.rendering")
            : showZipBoundaries
            ? t("map:boundaries.hide")
            : t("map:boundaries.show")}
        </button>
      </div>

      {/* 优化的弹窗样式 */}
      <style jsx global>{`
        /* Mapbox弹窗基础样式 */
        .mapboxgl-popup {
          max-width: 320px !important;
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
            sans-serif !important;
        }

        .mapboxgl-popup-content {
          padding: 0 !important;
          border-radius: 16px !important;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15),
            0 4px 12px rgba(0, 0, 0, 0.1) !important;
          border: 1px solid rgba(0, 0, 0, 0.05) !important;
          background: linear-gradient(
            135deg,
            #ffffff 0%,
            #f8fafc 100%
          ) !important;
          overflow: hidden !important;
          backdrop-filter: blur(10px) !important;
          transform: none !important;
          animation: none !important;
          transition: none !important;
        }

        .mapboxgl-popup-close-button {
          display: none !important;
        }

        .mapboxgl-popup-tip {
          border-top-color: white !important;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1)) !important;
        }

        /* 弹窗内容样式 */
        .daycare-popup {
          background: transparent;
          border-radius: 16px;
          overflow: hidden;
        }

        .popup-header {
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          color: white;
          padding: 16px 20px;
          position: relative;
          overflow: hidden;
        }

        .popup-header::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !important;
          pointer-events: none;
        }

        .popup-header h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          position: relative;
          z-index: 1;
        }

        .popup-header .zip-badge {
          display: inline-block;
          background: rgba(255, 255, 255, 0.2);
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;
          margin-top: 4px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .popup-content {
          padding: 20px;
          background: white;
        }

        .popup-stats {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
          margin-bottom: 16px;
        }

        .stat-item {
          text-align: center;
          padding: 12px;
          background: #f8fafc;
          border-radius: 12px;
          border: 1px solid #e2e8f0;
          transition: all 0.2s ease;
        }

        .stat-item:hover {
          background: #f1f5f9;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .stat-value {
          font-size: 20px;
          font-weight: 700;
          color: #1e293b;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 12px;
          color: #64748b;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .saturation-indicator {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 16px;
          background: #f8fafc;
          border-radius: 12px;
          border-left: 4px solid;
          margin-top: 12px;
        }

        .saturation-indicator.low {
          border-left-color: #22c55e;
          background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        }

        .saturation-indicator.medium {
          border-left-color: #eab308;
          background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
        }

        .saturation-indicator.high {
          border-left-color: #f97316;
          background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
        }

        .saturation-indicator.very-high {
          border-left-color: #ef4444;
          background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
        }

        .saturation-text {
          font-weight: 600;
          font-size: 14px;
        }

        .saturation-value {
          font-size: 18px;
          font-weight: 700;
        }

        .popup-close {
          position: absolute;
          top: 12px;
          right: 12px;
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;
          backdrop-filter: blur(10px);
          z-index: 2;
        }

        .popup-close:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(1.1);
        }

        .popup-close::before,
        .popup-close::after {
          content: "";
          position: absolute;
          width: 12px;
          height: 2px;
          background: white;
          border-radius: 1px;
        }

        .popup-close::before {
          transform: rotate(45deg);
        }

        .popup-close::after {
          transform: rotate(-45deg);
        }

        /* 移除动画效果，直接显示 */
        .mapboxgl-popup {
          /* 不使用动画，直接显示 */
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
          .mapboxgl-popup {
            max-width: 280px !important;
          }

          .popup-header {
            padding: 14px 16px;
          }

          .popup-content {
            padding: 16px;
          }

          .popup-stats {
            grid-template-columns: 1fr;
            gap: 12px;
          }
        }
      `}</style>
    </>
  );
};

export default MapContainer;
