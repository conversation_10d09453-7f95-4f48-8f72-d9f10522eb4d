/**
 * 工具函数库
 * 统一导出所有工具函数，提供便捷的访问接口
 */

import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

// 重新导出所有工具函数模块
export * from "./responsive";

// 从 theme 模块中分别导出类型和值，避免与 format 模块的 hexToRgb 函数冲突
export type { ColorScale, ThemeColors } from "./theme";
export {
  getSystemTheme,
  resolveTheme,
  generateColorScale,
  lighten,
  darken,
  rgbToHsl,
  getContrastColor,
  getContrastRatio,
  applyThemeVariables,
  createThemeTransition,
  getThemeVariable,
  themePresets,
  // 将 theme 模块的 hexToRgb 重命名为 themeHexToRgb 以避免冲突
  hexToRgb as themeHexToRgb,
} from "./theme";

/**
 * 合并 CSS 类名的工具函数
 * 支持 Tailwind CSS 类名冲突解决
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * 深拷贝对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T;
  }

  if (obj instanceof Array) {
    return obj.map((item) => deepClone(item)) as T;
  }

  if (typeof obj === "object") {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }

  return obj;
}

/**
 * 生成随机 ID
 */
export function generateId(prefix = "id"): string {
  return `${prefix}-${Math.random().toString(36).substring(2, 11)}`;
}

// 注意：大部分工具函数已按功能分类到对应的模块文件中

/**
 * 等待指定时间
 */
export function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * 重试函数
 */
export async function retry<T>(
  fn: () => Promise<T>,
  attempts: number = 3,
  delay: number = 1000
): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    if (attempts <= 1) {
      throw error;
    }
    await sleep(delay);
    return retry(fn, attempts - 1, delay);
  }
}
