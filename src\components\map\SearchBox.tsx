"use client";

import { MagnifyingGlassIcon, XMarkIcon } from "@heroicons/react/24/outline";
import React, { useState, useRef, useEffect } from "react";
import { useMapLanguage } from "@/hooks/useMapLanguage";
import type { SearchResult } from "@/types/daycare";

interface SearchBoxProps {
  value?: string;
  results?: SearchResult[];
  onSearch?: (query: string) => void;
  onResultSelect?: (result: SearchResult) => void;
  placeholder?: string;
  className?: string;
}

/**
 * 地址搜索框组件 - 使用Mapbox Geocoding API
 */
const SearchBox: React.FC<SearchBoxProps> = ({
  value = "",
  results = [],
  onSearch,
  onResultSelect,
  placeholder,
  className = "",
}) => {
  const { t } = useMapLanguage();
  const [query, setQuery] = useState(value);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [localResults, setLocalResults] = useState<SearchResult[]>(results);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceRef = useRef<NodeJS.Timeout | undefined>(undefined);

  // 同步外部value
  useEffect(() => {
    setQuery(value);
  }, [value]);

  // 同步外部results
  useEffect(() => {
    setLocalResults(results);
    setIsLoading(false);
  }, [results]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    console.log("Input changed:", newQuery); // 调试信息
    setQuery(newQuery);

    // 清除之前的防抖定时器
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    // 如果查询为空，清空结果
    if (!newQuery.trim()) {
      setLocalResults([]);
      setIsOpen(false);
      return;
    }

    // 设置防抖搜索
    setIsLoading(true);
    debounceRef.current = setTimeout(() => {
      performSearch(newQuery);
    }, 300);
  };

  // 执行搜索
  const performSearch = async (searchQuery: string) => {
    try {
      if (onSearch) {
        // 使用外部搜索处理器
        onSearch(searchQuery);
      } else {
        // 使用内置搜索
        const searchResults = await searchAddress(searchQuery);
        setLocalResults(searchResults);
      }
      setIsOpen(true);
    } catch (error) {
      console.error("Search error:", error);
      setLocalResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  // 内置地址搜索功能
  const searchAddress = async (
    searchQuery: string
  ): Promise<SearchResult[]> => {
    const accessToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;

    if (!accessToken) {
      throw new Error(t("map:errors.mapInitError"));
    }

    const response = await fetch(
      `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
        searchQuery
      )}.json?` +
        `access_token=${accessToken}&` +
        `country=US&` +
        `bbox=-124.4,32.5,-114.1,42.0&` + // 限制在加州范围
        `limit=5`
    );

    if (!response.ok) {
      throw new Error(t("map:errors.searchFailed"));
    }

    const data = await response.json();

    return data.features.map((feature: any) => ({
      id: feature.id,
      place_name: feature.place_name,
      center: feature.center,
      bbox: feature.bbox,
      context: feature.context,
    }));
  };

  // 处理结果选择
  const handleResultSelect = (result: SearchResult) => {
    setQuery(result.place_name);
    setIsOpen(false);
    onResultSelect?.(result);
  };

  // 清空搜索
  const handleClear = () => {
    setQuery("");
    setLocalResults([]);
    setIsOpen(false);
    inputRef.current?.focus();
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      setIsOpen(false);
      inputRef.current?.blur();
    }
  };

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchRef.current &&
        !searchRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // 清理防抖定时器
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      {/* 搜索输入框 */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
        </div>

        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => localResults.length > 0 && setIsOpen(true)}
          placeholder={placeholder || t("map:search.placeholder")}
          className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg bg-white shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm placeholder-gray-500"
          style={{
            color: "#111827 !important",
            backgroundColor: "#ffffff !important",
            WebkitTextFillColor: "#111827 !important",
          }} // 强制设置文字颜色和背景
        />

        {/* 清空按钮 */}
        {query && (
          <button
            onClick={handleClear}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            title={t("map:search.clearSearch")}
          >
            <XMarkIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
          </button>
        )}

        {/* 加载指示器 */}
        {isLoading && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          </div>
        )}
      </div>

      {/* 搜索结果下拉列表 */}
      {isOpen && localResults.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-auto">
          {localResults.map((result) => (
            <button
              key={result.id}
              onClick={() => handleResultSelect(result)}
              className="w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50
                         focus:outline-none border-b border-gray-100 last:border-b-0"
            >
              <div className="flex items-start">
                <MagnifyingGlassIcon className="h-4 w-4 text-gray-400 mt-0.5 mr-3 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {result.place_name.split(",")[0]}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    {result.place_name.split(",").slice(1).join(",").trim()}
                  </p>
                </div>
              </div>
            </button>
          ))}
        </div>
      )}

      {/* 无结果提示 */}
      {isOpen && !isLoading && query && localResults.length === 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
          <div className="px-4 py-3 text-sm text-gray-500 text-center">
            未找到相关地址
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchBox;
